# 邮箱功能新页签显示优化 - 测试指南

## 测试环境准备

### 1. 启动项目
```bash
npm run dev
# 或
yarn dev
```

### 2. 访问CRM邮箱模块
- 登录系统后，导航到 CRM 模块
- 点击左侧菜单中的"邮箱"选项
- 确认路径为 `/crm/email`

## 功能测试清单

### ✅ 基础显示测试

#### 1. 标签页导航显示
- [ ] 进入CRM邮箱页面时，顶部应显示标签页导航栏
- [ ] 默认显示"首页"和"邮件页"两个标签
- [ ] "邮件页"标签应处于激活状态

#### 2. 路径识别测试
- [ ] 在CRM模块中，点击"首页"标签应跳转到首页
- [ ] 在CRM模块中，点击"邮件页"标签应跳转到 `/crm/email`
- [ ] 如果有OA模块邮箱，验证在OA模块中标签页跳转到 `/oa/email`

### ✅ 新增线索功能测试

#### 1. 触发新增线索
- [ ] 在邮件列表中选择一封邮件
- [ ] 点击邮件工具栏中的"新增线索"按钮
- [ ] 应自动创建新的标签页，标题格式为"新增线索: [邮件主题前10字符]..."

#### 2. 线索表单显示
- [ ] 新标签页应显示线索创建表单
- [ ] 表单中应预填充邮件相关信息（发件人、主题等）
- [ ] 标签页应可关闭（显示关闭按钮）

#### 3. 保存和关闭
- [ ] 填写表单并点击"保存"按钮
- [ ] 应显示"线索创建成功"提示消息
- [ ] 标签页应自动关闭，返回邮件列表页面

### ✅ 全屏查看功能测试

#### 1. 触发全屏查看
- [ ] 在邮件列表中选择一封邮件
- [ ] 点击邮件工具栏中的"全屏查看"按钮
- [ ] 应创建新的全屏标签页，标题格式为"预览: [邮件主题前15字符]..."

#### 2. 全屏显示效果
- [ ] 新标签页应完整显示邮件内容
- [ ] 顶部应保留导航栏和标签页导航
- [ ] 邮件内容应正确显示（发件人、收件人、正文、附件等）
- [ ] 标签页应可关闭

#### 3. 全屏操作功能
- [ ] 点击"打印"按钮应能正常打印邮件
- [ ] 点击"关闭"按钮应关闭当前标签页
- [ ] 如果有附件，应能正常预览和下载

### ✅ 多标签页管理测试

#### 1. 多标签页创建
- [ ] 连续创建多个线索标签页，验证每个都能正常显示
- [ ] 连续创建多个全屏查看标签页，验证内容正确
- [ ] 混合创建线索和全屏查看标签页

#### 2. 标签页切换
- [ ] 点击不同标签页应能正确切换内容
- [ ] 每个标签页的内容应独立保持
- [ ] 标签页标题应正确显示

#### 3. 标签页关闭
- [ ] 点击标签页的关闭按钮应能正确关闭
- [ ] 关闭当前激活标签页时，应自动切换到前一个标签页
- [ ] 关闭所有自定义标签页后，应回到邮件页面

### ✅ 样式和布局测试

#### 1. 层级显示
- [ ] 标签页导航应始终显示在最上层
- [ ] 标签页内容不应覆盖导航栏
- [ ] 全屏模式下布局应正确

#### 2. 响应式测试
- [ ] 在不同屏幕尺寸下测试标签页显示
- [ ] 标签页过多时应支持横向滚动
- [ ] 移动端访问时布局应适配

#### 3. 交互反馈
- [ ] 鼠标悬停标签页时应有视觉反馈
- [ ] 点击标签页时应有激活状态显示
- [ ] 关闭按钮应有悬停效果

### ✅ 兼容性测试

#### 1. 浏览器兼容性
- [ ] Chrome 浏览器测试
- [ ] Firefox 浏览器测试
- [ ] Safari 浏览器测试（如适用）
- [ ] Edge 浏览器测试

#### 2. 功能兼容性
- [ ] 原有邮件功能不受影响
- [ ] 邮件列表、搜索、筛选功能正常
- [ ] 邮件编辑、发送功能正常

## 错误场景测试

### ⚠️ 异常情况处理

#### 1. 数据异常
- [ ] 邮件数据为空时的处理
- [ ] 邮件主题过长时的标题截断
- [ ] 网络异常时的错误提示

#### 2. 操作异常
- [ ] 快速连续点击按钮的防抖处理
- [ ] 同一邮件重复创建标签页的处理
- [ ] 标签页数量过多时的性能表现

## 性能测试

### 📊 性能指标

#### 1. 加载性能
- [ ] 标签页创建速度（应小于500ms）
- [ ] 标签页切换速度（应小于200ms）
- [ ] 内存使用情况（多标签页时）

#### 2. 用户体验
- [ ] 操作响应及时性
- [ ] 动画过渡流畅性
- [ ] 长时间使用稳定性

## 测试报告模板

### 测试结果记录
```
测试日期：[日期]
测试人员：[姓名]
测试环境：[浏览器版本/操作系统]

功能测试结果：
- 新增线索功能：✅ 通过 / ❌ 失败
- 全屏查看功能：✅ 通过 / ❌ 失败
- 多标签页管理：✅ 通过 / ❌ 失败
- 样式布局：✅ 通过 / ❌ 失败

发现问题：
1. [问题描述]
2. [问题描述]

建议改进：
1. [改进建议]
2. [改进建议]
```

## 常见问题排查

### 🔧 问题诊断

#### 1. 标签页不显示
- 检查路径是否为 `/crm/email`
- 确认 `showTabNavigation` 计算属性返回值
- 检查浏览器控制台是否有错误

#### 2. 事件总线问题
- 确认 `$bus.emit('add-tab')` 调用正确
- 检查 `OaLayout.vue` 中的事件监听器
- 验证 VueBus 插件是否正确安装

#### 3. 样式问题
- 检查 z-index 层级设置
- 确认 CSS 类名应用正确
- 验证响应式样式

#### 4. 路由问题
- 确认路由配置正确
- 检查路径判断逻辑
- 验证路由跳转参数

---

**注意事项：**
- 测试时请确保有足够的测试数据（邮件列表）
- 建议在不同网络环境下测试
- 记录测试过程中的任何异常情况
- 测试完成后请清理测试数据
