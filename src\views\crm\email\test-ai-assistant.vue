<template>
  <div class="test-ai-assistant">
    <div class="test-header">
      <h2>AI助手功能测试</h2>
      <button @click="toggleAiAssistant" class="toggle-btn">
        {{ showAiAssistant ? '关闭' : '打开' }} AI助手
      </button>
    </div>

    <div class="test-content">
      <div class="email-mock">
        <h3>模拟邮件内容</h3>
        <div class="email-item">
          <div class="email-subject">询价邮件 - 产品报价请求</div>
          <div class="email-content">
            尊敬的销售团队，<br><br>
            我们对贵公司的产品很感兴趣，希望能够获得以下产品的详细报价：<br>
            1. 产品型号A - 数量100件<br>
            2. 产品型号B - 数量50件<br><br>
            请提供价格、交付时间和付款条件。<br><br>
            期待您的回复。<br>
            张先生
          </div>
        </div>
      </div>

      <div class="function-test">
        <h3>功能测试区域</h3>
        <div class="test-info">
          <p><strong>新功能特点：</strong></p>
          <ul>
            <li>✅ 弹框左上角和右上角为圆角设计</li>
            <li>✅ 点击弹框外部区域可关闭弹框</li>
            <li>✅ 添加了半透明遮罩层</li>
            <li>✅ 优化了视觉效果和用户体验</li>
          </ul>
        </div>
        <div class="test-buttons">
          <button @click="testEmailSummary" class="test-btn">测试邮件摘要</button>
          <button @click="testAiWriting" class="test-btn">测试AI写信</button>
          <button @click="testAiPolish" class="test-btn">测试AI润色</button>
          <button @click="testAiReply" class="test-btn">测试AI回复</button>
          <button @click="testImageAnalysis" class="test-btn">测试图像分析</button>
          <button @click="testReportAnalysis" class="test-btn">测试报表分析</button>
        </div>
      </div>
    </div>

    <!-- AI助手抽屉 -->
    <AiAssistantDrawer
      :visible="showAiAssistant"
      :email="mockEmail"
      @close="showAiAssistant = false"
    />
  </div>
</template>

<script>
import AiAssistantDrawer from './components/AiAssistantDrawer.vue'

export default {
  name: 'TestAiAssistant',
  components: {
    AiAssistantDrawer
  },
  data() {
    return {
      showAiAssistant: false,
      mockEmail: {
        id: 1,
        subject: '询价邮件 - 产品报价请求',
        content: '尊敬的销售团队，我们对贵公司的产品很感兴趣，希望能够获得详细报价...',
        sender: '张先生',
        date: new Date()
      }
    }
  },
  methods: {
    toggleAiAssistant() {
      this.showAiAssistant = !this.showAiAssistant
    },

    testEmailSummary() {
      this.showAiAssistant = true
      // 可以在这里触发特定功能
    },

    testAiWriting() {
      this.showAiAssistant = true
    },

    testAiPolish() {
      this.showAiAssistant = true
    },

    testAiReply() {
      this.showAiAssistant = true
    },

    testImageAnalysis() {
      this.showAiAssistant = true
    },

    testReportAnalysis() {
      this.showAiAssistant = true
    }
  }
}
</script>

<style scoped>
.test-ai-assistant {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.test-header h2 {
  color: #303133;
  margin: 0;
}

.toggle-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.email-mock, .function-test {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
}

.email-mock h3, .function-test h3 {
  color: #303133;
  margin-top: 0;
  margin-bottom: 20px;
}

.email-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.email-subject {
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  font-size: 16px;
}

.email-content {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.test-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.test-btn {
  background: #f0f4ff;
  color: #667eea;
  border: 1px solid #667eea;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.test-info {
  background: #f0f4ff;
  border: 1px solid #667eea;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.test-info p {
  margin: 0 0 12px 0;
  color: #303133;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
