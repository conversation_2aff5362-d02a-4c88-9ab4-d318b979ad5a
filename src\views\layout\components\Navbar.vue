<template>
  <div class="navbar">
    <flexbox style="width: auto;">
      <el-popover
        v-if="dropMenus && dropMenus.length > 0"
        placement="bottom"
        width="320"
        trigger="click"
        popper-class="no-padding-popover">
        <div class="drop-wrap">
          <div class="drop-wrap__section">
            <div class="drop-wrap__title">切换到</div>
            <div class="drop-wrap__content">
              <flexbox
                v-for="(item, index) in dropMenus"
                :key="index"
                class="drop-cell is-interval"
                :class="[currentModule === item.module ? 'is-select' : '']"
                @click.native="dropClick(item)">
                <flexbox class="drop-cell__hd" justify="center">
                  <i :class="item.icon" />
                </flexbox>
                <div class="drop-cell__bd">
                  <div>{{ item.title }}</div>
                  <div class="des">{{ item.des }}</div>
                </div>
              </flexbox>
            </div>
          </div>
        </div>
        <el-button slot="reference" class="more" type="bg-icon" circle>
          <i class="wk wk-grid" />
        </el-button>
      </el-popover>
      <img
        :key="logo"
        v-src="logo"
        class="logo"
        @click="enterMainPage">
      <div v-if="title" class="title">{{ title }}</div>
      <div class="menus">
        <el-tabs
          v-model="menuTab"
          class="header-menu"
          @tab-click="navItemsClick">
          <el-tab-pane
            v-for="(item, index) in menus"
            :key="index"
            :index="item.path"
            :name="item.path"
            :label="item.label">
            <!-- <i :style="{ fontSize: item.fontSize }" :class="item.icon" /> -->
            <template slot="label">
              <router-link
                class="router-link"
                :to="{ path: item.fullPath }">
                <span
                  class="label">{{ item.label }}</span>
              </router-link><span v-if="item.num" type="primary" class="el-badge__content el-badge__content--undefined">{{ item.num }}</span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>
      <slot name="left" />
    </flexbox>

    <flexbox style="width: auto;">
      <div class="log-menus">
        <el-tooltip
        content="AI助手"
        effect="dark"
        placement="bottom">
        <el-button
          type="bg-icon"
          circle
          class="ai-assistant-btn"
          @click="toggleAiAssistant">
          <!-- <i class="wk wk-ai" /> -->
           <BotMessageSquare />
        </el-button>
      </el-tooltip>
        <el-tooltip
          v-for="(item, index) in logMenus"
          :key="index"
          :content="item.label"
          effect="dark"
          placement="bottom">
          <el-button
            :class="{'is-current': $route.path.includes(item.type) }"
            type="bg-icon"
            circle
            @click="logClick(item)">
            <i :class="item.icon" />
          </el-button>
        </el-tooltip>
      </div>

      <!-- <el-badge
        :value="unreadNums.announceCount"
        :hidden="!unreadNums.announceCount || unreadNums.announceCount == 0"
        :max="99"
        type="primary">
        <el-button type="bg-icon" circle @click="checkMessageDetail(true)">
          <i class="wk wk-announcement" />
        </el-button>
      </el-badge> -->
      <el-tooltip
        content="通知/待办"
        effect="dark"
        placement="bottom">
        <el-badge
          class="nav-badge"
          :value="messageCount"
          :hidden="messageCount === 0"
          :max="99"
          type="primary">
          <el-button type="bg-icon" circle @click="checkMessageDetail(false)">
            <i class="wk wk-bell" />
          </el-button>
        </el-badge>
      </el-tooltip>

      <el-tooltip
        content="帮助"
        effect="dark"
        placement="bottom">
        <el-button type="bg-icon" circle @click="enterHelp">
          <i class="wk wk-help" />
        </el-button>
      </el-tooltip>

      <el-tooltip
        content="系统设置"
        effect="dark"
        placement="bottom">
        <el-button v-if="manage" type="bg-icon" style="margin-left: 0;" circle @click="enterSystemSet">
          <i class="wk wk-manage" />
        </el-button>
      </el-tooltip>

      <!-- <system-message
        :visible.sync="sysMessageShow"
        :unread-nums="unreadNums"
        :only-announcement="mesOnlyAnnouncement"
        @update-count="sendSystemUnreadNum" /> -->
      <messages
        v-if="sysMessageShow"
        :unread-nums="unreadNums"
        @close="sysMessageShow = false"
        @update-unread="sendSystemUnreadNum"
      />

      <el-dropdown
        trigger="click"
        style="margin-left: 6px;"
        @command="moreMenuClick">
        <xr-avatar
          v-if="userInfo && Object.keys(userInfo).length > 0"
          :name="userInfo.realname"
          :size="24"
          :src="userInfo.img" />
        <!-- <i class="el-icon-caret-bottom mark"/> -->
        <el-dropdown-menu
          slot="dropdown"
          class="el-dropdown-unarrow">
          <el-dropdown-item
            v-for="(item, index) in moreMenu"
            :key="index"
            :command="item.command"
            :divided="item.divided"
            :icon="item.icon"
            :disabled="item.disabled"
            style="white-space: nowrap;">
            {{ item.label }}
          </el-dropdown-item>
          <!-- <div
            v-if="manage"
            class="handel-box">
            <el-button
              type="primary"
              class="handel-button"
              @click="enterSystemSet()">企业管理后台</el-button>
          </div> -->
        </el-dropdown-menu>
      </el-dropdown>
    </flexbox>
    <!-- 帮助中心 -->
    <help-side
      v-if="helpSideShow"
      :props="helpSideProps"
      @close="helpSideShow = false"
    />

    <!--增值服务 -->
    <appreciation-side
      v-if="appreciationSideShow"
      :props="appSideProps"
      @close="appreciationSideShow = false"
    />

    <!-- AI助手侧边栏 -->
    <ai-assistant-drawer
      :visible="showAiAssistant"
      @close="showAiAssistant = false"
    />
  </div>
</template>

<script>
import { systemMessageUnreadCountAPI } from '@/api/common'
import { BotMessageSquare } from 'lucide-vue';
import Messages from './Messages'
import HelpSide from './Help/HelpSide'
import AppreciationSide from './Appreciation/AppreciationSide'
import AiAssistantDrawer from '@/views/crm/email/components/AiAssistantDrawer.vue'

import { mapGetters, mapState } from 'vuex'
import { Loading } from 'element-ui'
import { on, off } from '@/utils/dom'
import { isEmpty } from '@/utils/types'
import { getNavMenus } from './utils'

export default {
  filters: {
    langName: function(value) {
      if (value) {
        return { cn: '中文', en: 'English' }[value]
      } else {
        return '中文'
      }
    }
  },
  components: {
    Messages,
    HelpSide,
    AppreciationSide,
    AiAssistantDrawer,
    BotMessageSquare
  },
  props: {
    title: String,
    menus: Array // 中间菜单数据
  },
  data() {
    return {
      unreadNums: {
        allCount: 0,
        announceCount: 0,
        crmCount: 0,
        examineCount: 0,
        eventCount: 0,
        logCount: 0,
        taskCount: 0
      },
      mesOnlyAnnouncement: false,
      sysMessageShow: false,
      intervalId: null,
      menuTab: '',
      helpSideProps: null,
      appSideProps: null,
      helpSideShow: false, // 帮助中心
      appreciationSideShow: false, // 增值服务
      showAiAssistant: false // AI助手侧边栏
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'lang',
      'logo',
      'crm',
      'bi',
      'manage',
      'oa',
      'collapse',
      'app',
      'oaRouters',
      'messageNum',
      'moduleAuth',
      'moduleData'
    ]),
    // 通知 + 代办事项总数量
    messageCount() {
      return (this.messageNum.totalNum || 0) + (this.unreadNums.allCount || 0)
    },
    moreMenu() {
      const temps = [{
        command: 'baseInfo',
        divided: false,
        label: '基本信息',
        icon: 'wk wk-user'
      }]
      return temps.concat([{
        command: 'logOut',
        divided: false,
        label: '退出登录',
        icon: 'wk wk-logout'
      }, {
        command: 'version',
        divided: false,
        label: `版本 ${WKConfig.version}`,
        icon: 'wk wk-version',
        disabled: true
      }])
    },
    //  左侧下拉数据
    dropMenus() {
      const allList = []
      for (const key in this.allItemsObj) {
        allList.push(this.allItemsObj[key])
      }
      return allList
    },
    allItemsObj() {
      var tempsItems = {}
      if (this.crm) {
        tempsItems.crm = {
          title: 'CRM',
          des: '客户关系管理系统',
          type: 1,
          module: 'crm',
          path: '/crm',
          icon: 'wk wk-customer',
          fontSize: '17px'
        }
      }

      // if (this.oa && this.oa.taskExamine) {
      //   tempsItems.taskExamine = {
      //     title: '任务/审批',
      //     type: 4,
      //     module: 'taskExamine',
      //     path: '/taskExamine',
      //     icon: 'wk wk-office',
      //     fontSize: '16px'
      //   }
      // }

      // if (this.oa && this.oa.log) {
      //   tempsItems.log = {
      //     title: '日志',
      //     type: 3,
      //     module: 'log',
      //     path: '/workLog',
      //     icon: 'wk wk-log',
      //     fontSize: '17px'
      //   }
      // }

      // if (this.oa && this.oa.book) {
      //   tempsItems.book = {
      //     title: '通讯录',
      //     type: 6,
      //     module: 'book',
      //     path: '/addressBook',
      //     icon: 'wk wk-address-book',
      //     fontSize: '17px'
      //   }
      // }

      // if (this.oa) {
      //   tempsItems.oa = {
      //     title: 'OA',
      //     type: 14,
      //     path: '/oa',
      //     module: 'oa',
      //     icon: 'wk wk-log',
      //     fontSize: '18px'
      //   }
      // }

      if (this.bi) {
        tempsItems.bi = {
          title: 'BI',
          des: '商业智能',
          type: 5,
          path: '/bi',
          module: 'bi',
          icon: 'wk wk-business-intelligence',
          fontSize: '18px'
        }
      }

      // if (this.oa && this.oa.calendar) {
      //   tempsItems.calendar = {
      //     title: '日历',
      //     type: 8,
      //     module: 'calendar',
      //     path: '/calendar/index',
      //     icon: 'wk wk-calendar',
      //     fontSize: '20px'
      //   }
      // }
      return tempsItems
    },

    /**
     * 日志菜单
     */
    logMenus() {
      const temps = []
      if (this.oa) {
        if (this.oa.taskExamine) {
          temps.push({
            icon: 'wk wk-task',
            label: '任务',
            type: 'oa/task',
            path: '/oa/task/subs/1'
          })
        }

        if (this.oa.taskExamine) {
          temps.push({
            icon: 'wk wk-approve',
            label: '审批',
            type: 'oa/examine',
            path: '/oa/examine/subs/my'
          })
        }

        if (this.oa.log) {
          temps.push({
            icon: 'wk wk-log',
            label: '日志',
            type: 'oa/log',
            path: '/oa/log/subs/all'
          })
        }

        if (this.oa.book) {
          temps.push({
            icon: 'wk wk-address-book',
            label: '通讯录',
            type: 'oa/book',
            path: '/oa/book/index'
          })
        }

        if (this.oa.calendar) {
          temps.push({
            icon: 'wk wk-icon-calendar-solid',
            label: '日历',
            type: 'oa/calendar',
            path: '/oa/calendar/index'
          })
        }
      }
      return temps
    },

    logLeftMenus() {
      return getNavMenus(this.oaRouters || [], '/oa')
    },

    // 获取当前模块关键字
    currentModule() {
      const { path, query } = this.$route
      let module = query.module
      if (!module) {
        const paths = path.split('/')
        console.log(paths)
        paths.forEach(o => {
          if (o && !module) {
            module = o
          }
        })
      }
      module = module ? module.toLowerCase() : ''
      const vilidModules = this.dropMenus.map(item => item.module)
      if (!vilidModules.includes(module)) {
        module = ''
      }
      return module
    }
  },
  watch: {
    $route: {
      handler(val) {
        const { path, query } = this.$route
        // 子菜单逻辑 解决这部分之前的 当做菜单
        if (path.includes('/subs/')) {
          this.menuTab = path.split('/subs/')[0]
        } else {
          this.menuTab = path
        }

        // 有些多个地方对应一个位置 query 里传入 navActiveMenu
        if (query && query.navActiveMenu) {
          this.menuTab = query.navActiveMenu
        }

        if (this.menus) {
          const isOa = path.includes('/oa')
          const menu = (isOa ? this.logLeftMenus : this.menus).find(item => item.path === this.menuTab)
          menu && this.$emit('select', menu, isOa)
        }
      },
      immediate: true
    }
  },
  created() {
    // this.menuTab = this.menus[0].path
    // this.$store.dispatch('QueryMarketing')
  },

  mounted() {
    on(document, 'mousedown', this.handleDocumentClick)

    // 消息数
    this.getSystemUnreadNum('visible')

    this.$bus.on('document-visibility', (state) => {
      this.getSystemUnreadNum(state)
    })
  },
  beforeDestroy() {
    this.$bus.off('document-visibility')
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    off(document, 'mousedown', this.handleDocumentClick)
  },
  methods: {
    navItemsClick(tab) {
      const menu = this.menus[parseInt(tab.index)]
      this.$router.push(menu.fullPath)
    },

    /**
     * 验证页面进入信息
     */
    validateEnterData(type) {
      return new Promise((resolve) => {
        resolve(true)
      })
    },

    enterSystemSet() {
      this.$router.push({
        name: 'manage'
      })
    },
    switchLang(item) {
      this.$store.commit('SET_LANG', item.lang)
      this.langName = item.name
    },

    /**
     * 获取系统未读消息数
     */
    getSystemUnreadNum(state) {
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = null
      }
      if (state == 'visible') {
        this.sendSystemUnreadNum()
        this.intervalId = setInterval(() => {
          this.sendSystemUnreadNum()
        }, 600000)
      }
    },

    sendSystemUnreadNum() {
      systemMessageUnreadCountAPI()
        .then(res => {
          this.unreadNums = res.data
        })
        .catch(() => {
          if (this.intervalId) {
            clearInterval(this.intervalId)
            this.intervalId = null
          }
        })
    },

    /**
     * 有客户权限点击logo 进入仪表盘
     */
    enterMainPage() {
      this.$router.push('/')
    },

    moreMenuClick(command) {
      if (command == 'baseInfo') {
        this.$router.push({
          name: 'person'
        })
      } else if (command == 'myOrder') {
        const time = Date.parse(new Date())
        this.$router.push({
          name: 'person',
          query: {
            selectedIndex: 2,
            time
          }
        })
      } else if (command == 'userPerson') {
        window.open('https://www.72crm.com/center')
      } else if (command == 'upgradeLog') {
        window.open('https://www.72crm.com/upgrade_log')
      } else if (command == 'help') {
        window.open('https://www.72crm.com/help')
      } else if (command == 'logOut') {
        this.$confirm('退出登录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            var loading = Loading.service({
              target: document.getElementById('#app')
            })
            this.$store
              .dispatch('LogOut')
              .then(() => {
                loading.close()
                location.reload()
              })
              .catch(() => {
                loading.close()
                location.reload()
              })
          })
          .catch(() => {})
      }
    },

    /**
     * 控制导航管理隐藏
     */
    handleDocumentClick(e) {
      // Safely check if className is a string or has toString method
      const className = e.target.className && typeof e.target.className === 'string'
        ? e.target.className
        : e.target.className && typeof e.target.className.baseVal === 'string'
          ? e.target.className.baseVal
          : e.target.className && typeof e.target.className.toString === 'function'
            ? e.target.className.toString()
            : '';

      if (className.includes('wk-icon-fill-help')) {
        const type = e.target.dataset.type
        const id = e.target.dataset.id
        if (type && id) {
          this.helpSideProps = {
            menuIndex: 'Question',
            type: parseInt(type),
            id: parseInt(id)
          }
          this.helpSideShow = true
        }
      }
      if (className.includes('wk-premium-info')) {
        const type = e.target.dataset.type
        const force = e.target.dataset.force // 如果等于1 强制查看
        if (type) {
          // 如果已经开启不查看简介 也就是数量大于0
          if (type === 'BusinessInformation' && force !== '1') {
            const enterprise = this.moduleData.find(item => item.module === 'enterprise')
            if (enterprise.number > 0) {
              return
            }
          }

          this.appSideProps = {
            type
          }
          this.appreciationSideShow = true
        }
      }
    },

    /**
     * 查看消息详情
     */
    checkMessageDetail(onlyAnnouncement) {
      this.mesOnlyAnnouncement = onlyAnnouncement
      this.sysMessageShow = true
    },

    /**
     * 模块切换
     */
    dropClick(item) {
      this.validateEnterData(item.path).then(result => {
        if (result) {
          this.$router.push(item.path)
        }
      })
    },

    /**
     * 日志点击
     */
    logClick(item) {
      let module = ''
      const { path, query } = this.$route
      console.log("我倒要看看+++++++",path,query);
      // 获取path中的模块关键词 一般是第一个
      if (path) {
        const paths = path.split('/')
        paths.forEach(o => {
          if (o && !module) {
            module = o
          }
        })
      }

      if (module === 'oa') {
        module = query.module ? query.module : this.getAuthModule()
      } else if (!module) {
        module = this.getAuthModule()
      }

      const nextPath = `${item.path}?module=${module}`
      console.log("我倒要看看11+++++++",nextPath);
      this.$router.push(nextPath)
    },

    /**
     * 获取有权限的模块
     */
    getAuthModule() {
      if (this.crm) {
        return 'crm'
      } else if (this.oa) {
        return 'oa'
      }
      return ''
    },

    /**
     * 展示help
     */
    enterHelp() {
      this.helpSideProps = null
      this.helpSideShow = true
    },

    /**
     * 切换AI助手
     */
    toggleAiAssistant() {
      this.showAiAssistant = !this.showAiAssistant
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.header-menu {
  .el-tabs__header {
    margin-bottom: 0;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__item {
    height: 56px;
    padding: 0 12px !important;
    line-height: 56px;

    .router-link {
      display: inline-block;
      user-select: none;
    }

    .label {
      position: relative;

      &:hover {
        color: $--color-primary;
      }
    }

    .label:hover::before {
      position: absolute;
      top: -4px;
      right: -4px;
      bottom: -4px;
      left: -4px;
      z-index: -1;
      content: "";
      background-color: $--color-b50;
      border-radius: $--border-radius-base;
    }
  }

  .el-tabs__active-bar {
    height: 3px;
  }

  .el-button--bg-text {
    padding-right: 4px;
    padding-left: 4px;
    font-weight: bold;

    [class^="el-icon-"] {
      margin-left: 2px;
      font-size: 12px;
      font-weight: bold;
      color: #97a0ae;
    }
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "style";

.navbar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 12px;
  background-color: white;

  .more + .logo {
    margin-left: 8px;
  }

  .logo {
    display: block;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    margin-left: $--interval-base;
    cursor: pointer;
    background-color: white;
  }

  .title {
    margin-right: 20px;
    margin-left: 8px;
    font-size: 18px;
    color: $--color-n600;
  }

  .menus {
  }

  // 模块切换图标
  .wk-grid {
    font-size: 16px;
  }

  .xr-avatar {
    cursor: pointer;
  }

  // .is-circle-bg {
  //   cursor: pointer;
  //   &:hover::before {
  //     left: -4px;
  //     right: -3px;
  //     top: -4px;
  //     bottom: -3px;
  //     border-radius: 16px;
  //     width: 32px;
  //     height: 32px;
  //   }
  // }
  // .user-container {
  //   display: flex;
  //   align-items: center;
  //   flex-shrink: 0;
  //   cursor: pointer;
  // .user-img {
  //   margin-right: 8px;
  // }
  // .mark {
  //   font-size: 15px;
  //   color: $--color-text-secondary;
  // }
  // }

  // .user-container:hover {
  //   .mark {
  //     color: #2486e4;
  //   }
  // }
}

// 菜单
// .el-menu {
//   overflow: hidden;

//   ::v-deep .el-submenu__icon-arrow {
//     display: none;
//   }

//   ::v-deep .el-submenu {
//     .el-submenu__title {
//       i {
//         color: #5c6075;
//       }
//     }
//   }

//   ::v-deep .el-submenu.is-active {
//     .el-submenu__title {
//       i {
//         color: $--color-primary;
//       }
//     }
//   }
// }

// .el-menu.el-menu--horizontal {
//   border-bottom: none;
// }

// .el-menu-item {
//   padding: 0;
//   margin: 0 20px;
//   font-size: 16px;
//   font-weight: 500;
//   color: #2a304d;
//   i {
//     color: #5c6075;
//   }
// }

// .el-menu-item:hover {
//   i {
//     color: $--color-primary;
//   }
// }

// .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
//   color: $--color-primary;
// }

// .el-menu-item.is-active {
//   border-width: 3px;
//   i {
//     color: $--color-primary;
//   }
// }

// 右侧操作
.handel-box {
  padding: 0 15px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;

  .handel-button {
    width: 100%;
    background-color: #2362fb;
    border-color: #2362fb;
    border-radius: $--border-radius-base;
  }
}

.hr-top {
  padding-top: 3px;
  margin-top: 8px;
  border-top: 1px solid #f4f4f4;
}

// .nav-lang {
//   cursor: pointer;
//   color: #888;
//   padding: 20px;
//   &:hover {
//     color: #2362fb;
//   }
//   &.active {
//     font-weight: bold;
//     color: #2362fb;
//   }
// }

// 系统消息
.wk-icon-gift,
.wk-announcement,
.wk-bell {
  font-size: 20px;
}

.el-badge {
  margin-right: 4px;
}

.wk-announcement:hover,
.wk-bell:hover {
  color: $--color-primary;
}

.el-dropdown-menu {
  ::v-deep .popper__arrow {
    display: none;
  }
}

::v-deep .nav-badge {
  .el-badge__content.is-fixed {
    top: 8px;
    right: 18px;
  }
}

.el-button.is-circle {
  padding: 6px;
  border: none;

  i {
    font-size: 20px;
  }

  &:hover {
    background-color: rgba($color:$--color-b50, $alpha: 0.9);
  }
}

.log-menus {
  position: relative;
  flex-shrink: 0;
  padding-right: $--interval-base;
  margin-right: $--interval-base;

  &::after {
    position: absolute;
    top: 6px;
    right: 0;
    bottom: 6px;
    width: 1px;
    content: " ";
    background-color: $--border-color-base;
  }
}

.el-button.is-current {
  color: $--color-primary;
  background-color: rgba(222, 235, 255, 0.9);
}

// .top-btn {
//   display: flex;
//   flex-direction: row;
//   justify-content: flex-start;
//   align-items: center;
//   color: $--color-primary;
//   .gift {
//     color: $--color-primary;
//     margin-right: 5px;
//   }
// }

// AI助手按钮样式
.ai-assistant-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: 1px solid #667eea !important;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
  }

  i {
    color: white !important;
  }

  // 调整AI助手图标大小，与其他按钮保持一致
  svg {
    width: 14px !important;
    height: 14px !important;
    color: white !important;
  }
}
</style>

