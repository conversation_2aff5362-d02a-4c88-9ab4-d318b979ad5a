<template>
  <div class="tab-navigation" v-if="showTabs">
    <div class="tab-container">
      <div
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tab"
        :class="{ 'active': activeTab === tab.id }"
        @click="switchTab(tab.id)">
        {{ tab.title }}
        <button
          v-if="tab.closable"
          class="close-tab"
          @click.stop="closeTab(tab.id, index)">
          <span class="close-icon">×</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabNavigation',
  props: {
    // 当前激活的标签
    activeTab: {
      type: String,
      default: 'home'
    },
    // 是否显示标签导航
    showTabs: {
      type: Boolean,
      default: true
    },
    // 标签页数组
    tabs: {
      type: Array,
      default: () => [
        { id: 'home', title: '首页', closable: false },
        { id: 'email', title: '邮件页', closable: false }
      ]
    }
  },
  methods: {
    switchTab(tabId) {
      // 发出事件通知父组件切换标签，父组件会处理路由跳转
      this.$emit('tab-change', tabId);
    },

    // 关闭标签页
    closeTab(tabId, index) {
      console.log("关闭标签页=======", tabId, index);
      this.$emit('tab-close', { tabId, index });
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-navigation {
  width: 100%;
  background-color: #fff;
  border-bottom: 1px solid #e6e9ed;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100 !important; /* 使用更高的z-index并添加!important确保优先级 */
}

.tab-container {
  display: flex;
  max-width: 1200px;
  padding: 0 16px;
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-container::-webkit-scrollbar {
  height: 4px;
}

.tab-container::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 4px;
}

.tab {
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  color: #606266;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  white-space: nowrap;

  &:hover {
    color: #409EFF;
  }

  &.active {
    color: #409EFF;
    font-weight: 500;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #409EFF;
    }
  }
}

.close-tab {
  margin-left: 8px;
  background: none;
  border: none;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #F56C6C;
  }
}

.close-icon {
  font-size: 14px;
  line-height: 1;
}
</style>
