<template>
  <el-container>
    <el-header class="nav-container">
      <navbar
        v-if="menus && menus.length > 0"
        :menus="menus"
        title="CRM"
        @select="menuSelect"
      >
        <el-popover
          v-if="quickAddList.length"
          slot="left"
          placement="bottom"
          width="160"
          trigger="click"
          popper-class="no-padding-popover">
          <div class="drop-wrap is-small">
            <div class="drop-wrap__section">
              <div class="drop-wrap__content">
                <flexbox
                  v-for="(item, index) in quickAddList"
                  :key="index"
                  class="drop-cell is-small"
                  @click.native="addSkip(item)">
                  <flexbox class="drop-cell__hd" justify="center">
                    <i :class="`wk wk-${item.icon}`" />
                  </flexbox>
                  <div class="drop-cell__bd">{{ item.label }}</div>
                </flexbox>
              </div>
            </div>
          </div>
          <el-button
            slot="reference"
            class="common-create-btn"
            type="primary">新建</el-button>
        </el-popover>
      </navbar>
    </el-header>

    <!-- 添加Tab导航区域，仅在邮件页面显示 -->
    <tab-navigation
      v-if="showTabNavigation"
      :active-tab="currentTab"
      :tabs="tabPages"
      @tab-change="handleTabChange"
      @tab-close="handleTabClose"
    />

    <!-- 标签页内容区域 -->
    <div v-if="showTabContent" class="tab-content-container">
      <tab-content
        :tab="currentTabData"
        :tags="emailTags"
        @close="handleTabClose"
        @save-success="handleTabSaveSuccess"
      />
    </div>

    <wk-container
      :menu="sideMenus"
      :header-obj="headerCellObj"
    >
      <el-main
        id="crm-main-container"
        style="padding: 0;">
        <app-main />
      </el-main>
    </wk-container>
    <c-r-m-all-create
      v-if="isCreate"
      :crm-type="createCRMType"
      :action="createAction"
      @save-success="createSaveSuccess"
      @close="isCreate=false"
    />
  </el-container>
</template>

<script>
import { Navbar, AppMain } from './components'
import WkContainer from './components/WkContainer'
import CRMAllCreate from '@/views/crm/components/CRMAllCreate'
import TabNavigation from '@/components/TabNavigation'
import TabContent from '@/components/TabContent'

import { mapGetters } from 'vuex'
import { getNavMenus } from './components/utils'
import path from 'path'

export default {
  name: 'CrmLayout',

  components: {
    Navbar,
    AppMain,
    CRMAllCreate,
    WkContainer,
    TabNavigation,
    TabContent
  },

  data() {
    return {
      isCreate: false,
      createAction: null,
      createCRMType: '',
      sideChildRouter: null, // 包含child的路由对象
      sideMenus: [],
      // 标签页相关数据
      currentTab: 'home', // 当前激活的标签，默认为首页
      tabPages: [
        { id: 'home', title: '首页', closable: false },
        { id: 'email', title: '邮件', closable: false }
      ]
    }
  },

  computed: {
    ...mapGetters(['crm', 'crmRouters']),
    menus() {
      return getNavMenus(this.crmRouters || [], '/crm')
    },
    // 快捷添加
    quickAddList() {
      var addItems = []
      if (this.crm.leads && this.crm.leads.save) {
        addItems.push({
          icon: 'leads-line',
          index: 'leads',
          label: '线索'
        })
      }
      if (this.crm.customer && this.crm.customer.save) {
        addItems.push({
          icon: 'customer-line',
          index: 'customer',
          label: '客户'
        })
      }
      if (this.crm.contacts && this.crm.contacts.save) {
        addItems.push({
          icon: 'contacts-line',
          index: 'contacts',
          label: '联系人'
        })
      }
      if (this.crm.business && this.crm.business.save) {
        addItems.push({
          icon: 'business-line',
          index: 'business',
          label: '商机'
        })
      }

      if (this.crm.contract && this.crm.contract.save) {
        addItems.push({
          icon: 'contract-line',
          index: 'contract',
          label: '合同'
        })
      }
      if (this.crm.receivables && this.crm.receivables.save) {
        addItems.push({
          icon: 'receivables-line',
          index: 'receivables',
          label: '回款'
        })
      }
      if (this.crm.invoice && this.crm.invoice.save) {
        addItems.push({
          icon: 'invoice-line',
          index: 'invoice',
          label: '发票'
        })
      }
      if (this.crm.visit && this.crm.visit.save) {
        addItems.push({
          icon: 'visit-line',
          index: 'visit',
          label: '回访'
        })
      }
      if (this.crm.product && this.crm.product.save) {
        addItems.push({
          icon: 'product-line',
          index: 'product',
          label: '产品'
        })
      }
      return addItems
    },
    headerCellObj() {
      const { path } = this.$route
      if (path.includes('customer')) {
        return {
          icon: 'wk wk-customer',
          label: '客户',
          des: '客户管理'
        }
      } else if (path.includes('receivables')) {
        return {
          icon: 'wk wk-receivables',
          label: '回款',
          des: '回款管理'
        }
      } else if (path.includes('message')) {
        return {
          icon: 'wk wk-todo-solid',
          label: '待办事项',
          des: '待办事项管理'
        }
      } else if (path.includes('crm/email')) {
        return {
          icon: 'wk wk-email',
          label: '邮箱',
          des: '邮箱管理'
        }
      }
      return null
    },

    // 是否显示Tab导航
    showTabNavigation() {
      // 在邮件页面或有自定义标签页时显示Tab导航
      const isEmailPage = this.$route.path.includes('crm/email');
      const hasCustomTabs = this.tabPages.length > 2; // 超过默认的首页和邮件页
      return isEmailPage || hasCustomTabs;
    },

    // 是否显示标签页内容
    showTabContent() {
      // 当前标签页不是首页或邮件页时显示标签页内容
      return this.currentTab !== 'home' && this.currentTab !== 'email';
    },

    // 当前标签页数据
    currentTabData() {
      return this.tabPages.find(tab => tab.id === this.currentTab) || {};
    },

    // 邮件标签数据
    emailTags() {
      // 这里可以从Vuex或其他地方获取邮件标签数据
      return [
        { id: 1, name: '通知', color: '#e60012' },
        { id: 2, name: '招聘', color: '#e60012' },
        { id: 3, name: '商机', color: '#e60012' },
        { id: 4, name: '报价', color: '#e60012' },
        { id: 5, name: '已更回复', color: '#e60012' },
        { id: 6, name: 'PI', color: '#e60012' },
        { id: 7, name: '订单', color: '#e60012' },
        { id: 8, name: '样品', color: '#e60012' },
        { id: 9, name: '询盘', color: '#e60012' }
      ];
    }
  },

  watch: {
    $route: {
      handler(route) {
        // 根据路由更新当前激活的标签
        if (route.path === '/' || route.path.startsWith('/home')) {
          // 只有在没有自定义标签页激活时才切换到首页
          if (!this.currentTab.startsWith('clue-') &&
              !this.currentTab.startsWith('fullscreen-') &&
              !this.currentTab.startsWith('order-')) {
            this.currentTab = 'home'
          }
        } else if (route.path.includes('/crm/email')) {
          // 只有在没有自定义标签页激活时才切换到邮件页
          if (!this.currentTab.startsWith('clue-') &&
              !this.currentTab.startsWith('fullscreen-') &&
              !this.currentTab.startsWith('order-')) {
            this.currentTab = 'email'
          }
        }
      },
      immediate: true
    }
  },

  created() {
    this.getcrmMessagNum()
  },

  mounted() {
    this.initEventListeners()
  },

  methods: {
    /**
     * 菜单选择
     */
    menuSelect(menu) {
      const router = this.crmRouters[menu.index]
      if (router && router.children && router.children.length > 1) {
        this.sideChildRouter = router
        this.sideMenus = this.getSideMenus(router.path, router.children)
      } else {
        this.sideChildRouter = null
        this.sideMenus = []
      }
    },

    /**
     * 获取siderMenus
     */
    getSideMenus(mainPath, children) {
      const sideMenus = []
      children.forEach(item => {
        let auth = true
        if (item.permissions) {
          auth = this.$auth(item.permissions.join('.'))
        }
        if (!item.hidden && auth) {
          sideMenus.push({
            ...item,
            path: path.resolve(mainPath, item.path)
          })
        }
      })
      return sideMenus
    },

    navClick(index) {},

    addSkip(item) {
      this.createAction = {
        type: 'save',
        id: '',
        data: {}
      }
      this.createCRMType = item.index
      this.isCreate = true
    },

    /**
     * 获取消息数
     */
    getcrmMessagNum() {
      this.$store
        .dispatch('GetMessageNum')
    },

    /**
     * 新建客户同时新建联系人
     */
    createSaveSuccess(data) {
      if (data && data.createContacts) {
        if (data.type == 'customer') {
          this.createCRMType = 'contacts'
          this.createAction = {
            type: 'relative',
            crmType: 'customer',
            data: {
              customer: data.data
            }
          }
          this.isCreate = true
        }
      }
    },

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
      // 监听添加标签页事件
      console.log('🔥 CrmLayout 初始化事件监听器');
      this.$bus.on('add-tab', (tab) => {
        console.log('🔥 CrmLayout 收到 add-tab 事件', tab);
        this.addTab(tab);
      });
    },

    /**
     * 处理Tab切换
     */
    handleTabChange(tabId) {
      this.currentTab = tabId

      // 根据tab切换路由
      if (tabId === 'home') {
        this.$router.push('/')
      } else if (tabId === 'email') {
        this.$router.push('/crm/email')
      } else if (tabId.startsWith('clue-') ||
                 tabId.startsWith('fullscreen-') ||
                 tabId.startsWith('order-')) {
        // 处理自定义标签页，不需要切换路由，保持当前页面
        // 如果当前不在邮件页面，则跳转到邮件页面以确保有正确的上下文
        if (!this.$route.path.includes('/email')) {
          this.$router.push('/crm/email')
        }
      }
    },

    /**
     * 处理Tab关闭
     */
    handleTabClose({ tabId, index }) {
      // 获取要关闭的标签页
      const closingTab = this.tabPages.find(tab => tab.id === tabId);
      const isFullscreen = closingTab && closingTab.isFullscreen;

      // 从标签页数组中移除该标签页
      this.tabPages = this.tabPages.filter(tab => tab.id !== tabId);

      // 如果关闭的是当前激活的标签页，则切换到前一个标签页
      if (this.currentTab === tabId) {
        // 重新计算索引，因为数组已经变化
        const newActiveIndex = Math.min(index, this.tabPages.length - 1);
        const newActiveTab = this.tabPages[newActiveIndex];

        if (newActiveTab) {
          this.currentTab = newActiveTab.id;
          // 如果切换到了首页或邮件页，需要相应地切换路由
          this.handleTabChange(this.currentTab);
        }

        // 如果关闭的是全屏标签页，移除全屏样式类
        if (isFullscreen) {
          this.$nextTick(() => {
            const container = document.querySelector('.tab-content-container');
            if (container) {
              container.classList.remove('fullscreen-mode');
            }
          });
        }
      }

      // 强制更新视图以确保tab导航状态正确
      this.$forceUpdate();
    },

    /**
     * 添加新标签页
     */
    addTab(tab) {
      console.log('🔥 CrmLayout addTab 方法被调用', tab);
      console.log('🔥 CrmLayout 当前标签页数组', this.tabPages);

      // 检查标签页是否已存在
      const existingTab = this.tabPages.find(t => t.id === tab.id);
      if (!existingTab) {
        this.tabPages.push(tab);
        console.log('🔥 CrmLayout 添加新标签页', tab);
      } else {
        // 如果标签页已存在，更新其内容
        const index = this.tabPages.findIndex(t => t.id === tab.id);
        this.tabPages.splice(index, 1, { ...existingTab, ...tab });
        console.log('🔥 CrmLayout 更新现有标签页', tab);
      }

      // 激活新标签页
      this.currentTab = tab.id;
      console.log('🔥 CrmLayout 设置当前标签页为', tab.id);
      console.log('🔥 CrmLayout 标签页数组长度', this.tabPages.length);
      console.log('🔥 CrmLayout showTabNavigation 计算结果', this.showTabNavigation);

      // 强制更新视图以确保tab导航显示
      this.$forceUpdate();

      // 如果是全屏模式，添加全屏样式类
      if (tab.isFullscreen) {
        this.$nextTick(() => {
          // 添加全屏样式
          const container = document.querySelector('.tab-content-container');
          if (container) {
            container.classList.add('fullscreen-mode');
          }
        });
      } else {
        this.$nextTick(() => {
          const container = document.querySelector('.tab-content-container');
          if (container) {
            container.classList.remove('fullscreen-mode');
          }
        });
      }
    },

    /**
     * 处理标签页保存成功
     */
    handleTabSaveSuccess({ tabId, data }) {
      console.log('CrmLayout 标签页保存成功:', tabId, data);

      // 这里可以根据不同的标签页类型处理不同的保存逻辑
      if (tabId.startsWith('clue-')) {
        // 处理线索保存成功
        this.$message.success('线索创建成功');
      } else if (tabId.startsWith('order-')) {
        // 处理订单保存成功
        this.$message.success('订单创建成功');
      }

      // 关闭标签页
      this.tabPages = this.tabPages.filter(tab => tab.id !== tabId);

      // 切换到邮件标签页
      this.currentTab = 'email';
      this.$router.push('/crm/email');
    }
  },

  beforeDestroy() {
    // 清理事件监听器
    this.$bus.off('add-tab');
  }
}
</script>

<style lang="scss" scoped>
@import "./components/style";
@import "./styles/common.scss";

.el-container {
  height: 100%;
  min-height: 0;
}

.nav-container {
  min-width: 1200px;
  padding: 0;
  box-shadow: 0 1px 2px #dbdbdb;
}

// 公共新建按钮
.common-create-btn {
  margin-left: #{$--interval-base * 2};
}

/* 标签页内容容器样式 */
.tab-content-container {
  position: relative;
  z-index: 100;
  background-color: #fff;
  border-bottom: 1px solid #e6e9ed;
}

/* 全屏模式样式 */
.tab-content-container.fullscreen-mode {
  position: fixed;
  top: 60px; /* 保留顶部导航栏高度 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: #fff;
}
</style>
